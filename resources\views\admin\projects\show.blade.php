@extends('layouts.dashboard')

@section('title', 'Project Details - ' . $project->title)

@section('content')
<div class="p-6 space-y-6">
    <!-- Header -->
    <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-4 sm:space-y-0">
        <div>
            <nav class="flex items-center space-x-2 text-sm text-gray-500 mb-2">
                <a href="{{ route('admin.projects.index') }}" class="hover:text-gray-700">Projects</a>
                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                </svg>
                <span class="text-gray-900">{{ $project->title }}</span>
            </nav>
            <h1 class="text-2xl font-bold text-gray-900">{{ $project->title }}</h1>
            <p class="text-gray-600">{{ $project->description }}</p>
        </div>
        <div class="flex space-x-3">
            <a href="{{ route('admin.projects.edit', $project) }}"
               class="inline-flex items-center px-4 py-2 bg-primary-600 border border-transparent rounded-lg font-medium text-white hover:bg-primary-700 transition-colors duration-200">
                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"/>
                </svg>
                Edit Project
            </a>
            @if($project->is_published)
                <a href="{{ route('projects.show', $project->slug) }}" target="_blank"
                   class="inline-flex items-center px-4 py-2 bg-green-600 border border-transparent rounded-lg font-medium text-white hover:bg-green-700 transition-colors duration-200">
                    <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14"/>
                    </svg>
                    View Public Page
                </a>
            @endif
        </div>
    </div>

    <!-- Project Status Cards -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
        <!-- Status -->
        <div class="bg-white rounded-lg border border-neutral-200 p-6">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-8 h-8 rounded-full flex items-center justify-center
                        @if($project->status === 'completed') bg-green-100
                        @elseif($project->status === 'in_progress') bg-blue-100
                        @elseif($project->status === 'planning') bg-yellow-100
                        @elseif($project->status === 'review') bg-purple-100
                        @elseif($project->status === 'on_hold') bg-orange-100
                        @else bg-gray-100
                        @endif">
                        <svg class="w-4 h-4
                            @if($project->status === 'completed') text-green-600
                            @elseif($project->status === 'in_progress') text-blue-600
                            @elseif($project->status === 'planning') text-yellow-600
                            @elseif($project->status === 'review') text-purple-600
                            @elseif($project->status === 'on_hold') text-orange-600
                            @else text-gray-600
                            @endif" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                        </svg>
                    </div>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-500">Status</p>
                    <p class="text-lg font-semibold text-gray-900">{{ ucfirst(str_replace('_', ' ', $project->status)) }}</p>
                </div>
            </div>
        </div>

        <!-- Progress -->
        <div class="bg-white rounded-lg border border-neutral-200 p-6">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                        <svg class="w-4 h-4 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"/>
                        </svg>
                    </div>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-500">Progress</p>
                    @if($project->estimated_hours > 0)
                        @php
                            $progress = min(100, ($project->actual_hours / $project->estimated_hours) * 100);
                        @endphp
                        <p class="text-lg font-semibold text-gray-900">{{ number_format($progress, 1) }}%</p>
                    @else
                        <p class="text-lg font-semibold text-gray-900">-</p>
                    @endif
                </div>
            </div>
        </div>

        <!-- Budget -->
        <div class="bg-white rounded-lg border border-neutral-200 p-6">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                        <svg class="w-4 h-4 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"/>
                        </svg>
                    </div>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-500">Total Amount</p>
                    <p class="text-lg font-semibold text-gray-900">
                        {{ $project->currency_code ?? 'ZAR' }} {{ number_format($project->total_amount, 2) }}
                    </p>
                </div>
            </div>
        </div>

        <!-- Visibility -->
        <div class="bg-white rounded-lg border border-neutral-200 p-6">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-8 h-8 {{ $project->is_published ? 'bg-green-100' : 'bg-gray-100' }} rounded-full flex items-center justify-center">
                        <svg class="w-4 h-4 {{ $project->is_published ? 'text-green-600' : 'text-gray-600' }}" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            @if($project->is_published)
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"/>
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"/>
                            @else
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L3 3m6.878 6.878L21 21"/>
                            @endif
                        </svg>
                    </div>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-500">Visibility</p>
                    <p class="text-lg font-semibold text-gray-900">{{ $project->is_published ? 'Public' : 'Private' }}</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Main Content -->
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <!-- Project Details -->
        <div class="lg:col-span-2 space-y-6">
            <!-- Featured Image -->
            @if($project->featured_image)
                <div class="bg-white rounded-lg border border-neutral-200 p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">Featured Image</h3>
                    <img src="{{ asset('storage/' . $project->featured_image) }}" 
                         alt="{{ $project->title }}" 
                         class="w-full h-64 object-cover rounded-lg">
                </div>
            @endif

            <!-- Project Content -->
            <div class="bg-white rounded-lg border border-neutral-200 p-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">Project Description</h3>
                @if($project->content)
                    <div class="prose max-w-none text-gray-700">
                        {!! nl2br(e($project->content)) !!}
                    </div>
                @else
                    <p class="text-gray-700">{{ $project->description }}</p>
                @endif
            </div>

            <!-- Time Tracking -->
            <div class="bg-white rounded-lg border border-neutral-200 p-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">Time & Budget Tracking</h3>
                <div class="grid grid-cols-2 gap-4">
                    <div>
                        <dt class="text-sm font-medium text-gray-500">Estimated Hours</dt>
                        <dd class="text-lg text-gray-900">{{ $project->estimated_hours ?? '-' }}</dd>
                    </div>
                    <div>
                        <dt class="text-sm font-medium text-gray-500">Actual Hours</dt>
                        <dd class="text-lg text-gray-900">{{ $project->actual_hours ?? '-' }}</dd>
                    </div>
                    <div>
                        <dt class="text-sm font-medium text-gray-500">Hourly Rate</dt>
                        <dd class="text-lg text-gray-900">
                            {{ $project->currency_code ?? 'ZAR' }} {{ number_format($project->hourly_rate, 2) }}
                        </dd>
                    </div>
                    <div>
                        <dt class="text-sm font-medium text-gray-500">Total Amount</dt>
                        <dd class="text-lg text-gray-900">
                            {{ $project->currency_code ?? 'ZAR' }} {{ number_format($project->total_amount, 2) }}
                        </dd>
                    </div>
                </div>
                
                @if($project->estimated_hours > 0)
                    <div class="mt-4">
                        <div class="flex justify-between text-sm text-gray-600 mb-1">
                            <span>Progress</span>
                            <span>{{ $project->actual_hours }}/{{ $project->estimated_hours }} hours</span>
                        </div>
                        @php
                            $progress = min(100, ($project->actual_hours / $project->estimated_hours) * 100);
                        @endphp
                        <div class="w-full bg-gray-200 rounded-full h-2">
                            <div class="bg-blue-600 h-2 rounded-full" style="width: {{ $progress }}%"></div>
                        </div>
                    </div>
                @endif
            </div>
        </div>

        <!-- Sidebar -->
        <div class="space-y-6">
            <!-- Project Info -->
            <div class="bg-white rounded-lg border border-neutral-200 p-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">Project Information</h3>
                <dl class="space-y-3">
                    <div>
                        <dt class="text-sm font-medium text-gray-500">Client</dt>
                        <dd class="text-sm text-gray-900">{{ $project->client_name }}</dd>
                        @if($project->client)
                            <dd class="text-sm text-gray-500">{{ $project->client->email }}</dd>
                        @endif
                    </div>
                    
                    @if($project->service)
                        <div>
                            <dt class="text-sm font-medium text-gray-500">Service</dt>
                            <dd class="text-sm text-gray-900">{{ $project->service->name }}</dd>
                        </div>
                    @endif
                    
                    @if($project->start_date)
                        <div>
                            <dt class="text-sm font-medium text-gray-500">Start Date</dt>
                            <dd class="text-sm text-gray-900">{{ $project->start_date->format('M d, Y') }}</dd>
                        </div>
                    @endif
                    
                    @if($project->end_date)
                        <div>
                            <dt class="text-sm font-medium text-gray-500">End Date</dt>
                            <dd class="text-sm text-gray-900">{{ $project->end_date->format('M d, Y') }}</dd>
                        </div>
                    @endif
                    
                    <div>
                        <dt class="text-sm font-medium text-gray-500">Priority</dt>
                        <dd class="text-sm">
                            <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium
                                @if($project->priority === 'urgent') bg-red-100 text-red-800
                                @elseif($project->priority === 'high') bg-orange-100 text-orange-800
                                @elseif($project->priority === 'medium') bg-yellow-100 text-yellow-800
                                @else bg-green-100 text-green-800
                                @endif">
                                {{ ucfirst($project->priority ?? 'low') }}
                            </span>
                        </dd>
                    </div>
                    
                    @if($project->project_url)
                        <div>
                            <dt class="text-sm font-medium text-gray-500">Project URL</dt>
                            <dd class="text-sm">
                                <a href="{{ $project->project_url }}" target="_blank" 
                                   class="text-primary-600 hover:text-primary-800 break-all">
                                    {{ $project->project_url }}
                                </a>
                            </dd>
                        </div>
                    @endif
                </dl>
            </div>

            <!-- Quick Actions -->
            <div class="bg-white rounded-lg border border-neutral-200 p-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">Quick Actions</h3>
                <div class="space-y-3">
                    <!-- Published Toggle -->
                    <button type="button"
                            onclick="togglePublished({{ $project->id }})"
                            class="w-full flex items-center justify-between px-3 py-2 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors duration-200">
                        <span class="text-sm font-medium text-gray-700">
                            {{ $project->is_published ? 'Unpublish' : 'Publish' }} Project
                        </span>
                        <div class="toggle-btn {{ $project->is_published ? 'toggle-btn-on' : 'toggle-btn-off' }}">
                            <div class="toggle-switch {{ $project->is_published ? 'translate-x-5' : 'translate-x-0' }}"></div>
                        </div>
                    </button>

                    <!-- Featured Toggle -->
                    <button type="button"
                            onclick="toggleFeatured({{ $project->id }})"
                            class="w-full flex items-center justify-between px-3 py-2 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors duration-200">
                        <span class="text-sm font-medium text-gray-700">
                            {{ $project->is_featured ? 'Remove from Featured' : 'Mark as Featured' }}
                        </span>
                        <svg class="w-5 h-5 {{ $project->is_featured ? 'text-yellow-500 fill-current' : 'text-gray-400' }}" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z"/>
                        </svg>
                    </button>

                    <!-- Delete Project -->
                    <form action="{{ route('admin.projects.destroy', $project) }}" method="POST" class="w-full">
                        @csrf
                        @method('DELETE')
                        <button type="submit"
                                onclick="return confirm('Are you sure you want to delete this project?')"
                                class="w-full flex items-center justify-center px-3 py-2 border border-red-300 text-red-700 rounded-lg hover:bg-red-50 transition-colors duration-200">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"/>
                            </svg>
                            Delete Project
                        </button>
                    </form>
                </div>
            </div>

            <!-- SEO Information -->
            @if($project->meta_title || $project->meta_description || $project->meta_keywords)
                <div class="bg-white rounded-lg border border-neutral-200 p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">SEO Information</h3>
                    <dl class="space-y-3">
                        @if($project->meta_title)
                            <div>
                                <dt class="text-sm font-medium text-gray-500">Meta Title</dt>
                                <dd class="text-sm text-gray-900">{{ $project->meta_title }}</dd>
                            </div>
                        @endif
                        
                        @if($project->meta_description)
                            <div>
                                <dt class="text-sm font-medium text-gray-500">Meta Description</dt>
                                <dd class="text-sm text-gray-900">{{ $project->meta_description }}</dd>
                            </div>
                        @endif
                        
                        @if($project->meta_keywords)
                            <div>
                                <dt class="text-sm font-medium text-gray-500">Meta Keywords</dt>
                                <dd class="text-sm text-gray-900">{{ $project->meta_keywords }}</dd>
                            </div>
                        @endif
                    </dl>
                </div>
            @endif
        </div>
    </div>
</div>

@push('styles')
<style>
.toggle-btn {
    @apply relative inline-flex h-6 w-11 flex-shrink-0 cursor-pointer rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2;
}

.toggle-btn-on {
    @apply bg-primary-600;
}

.toggle-btn-off {
    @apply bg-gray-200;
}

.toggle-switch {
    @apply pointer-events-none inline-block h-5 w-5 transform rounded-full bg-white shadow ring-0 transition duration-200 ease-in-out;
}
</style>
@endpush

@push('scripts')
<script>
function togglePublished(projectId) {
    fetch(`/admin/projects/${projectId}/toggle-published`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            location.reload();
        } else {
            alert('Error updating project status');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('Error updating project status');
    });
}

function toggleFeatured(projectId) {
    fetch(`/admin/projects/${projectId}/toggle-featured`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            location.reload();
        } else {
            alert('Error updating featured status');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('Error updating featured status');
    });
}
</script>
@endpush
@endsection
