<?php

namespace Database\Seeders;

use App\Models\Project;
use App\Models\Service;
use App\Models\User;
use App\Models\Role;
use Illuminate\Database\Seeder;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\DB;

class ProjectSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // First, create services if they don't exist
        $this->createServices();
        
        // Then create client users if they don't exist
        $this->createClientUsers();
        
        // Finally, create projects for each service
        $this->createProjects();
    }

    /**
     * Create services for the projects.
     */
    private function createServices(): void
    {
        $services = [
            [
                'name' => 'Web Development',
                'slug' => 'web-development',
                'short_description' => 'Custom website development and web applications',
                'description' => 'Professional web development services including responsive websites, web applications, and e-commerce platforms using modern technologies like Laravel, React, and Vue.js.',
                'icon' => 'fas fa-code',
                'price_from' => 5000.00,
                'features' => ['Responsive Design', 'SEO Optimization', 'Content Management', 'Performance Optimization'],
                'is_featured' => true,
                'is_active' => true,
                'sort_order' => 1,
            ],
            [
                'name' => 'Mobile App Development',
                'slug' => 'mobile-app-development',
                'short_description' => 'Native and cross-platform mobile applications',
                'description' => 'Create powerful mobile applications for iOS and Android platforms using React Native, Flutter, or native development approaches.',
                'icon' => 'fas fa-mobile-alt',
                'price_from' => 15000.00,
                'features' => ['Cross-platform', 'Native Performance', 'App Store Deployment', 'Push Notifications'],
                'is_featured' => true,
                'is_active' => true,
                'sort_order' => 2,
            ],
            [
                'name' => 'E-commerce Solutions',
                'slug' => 'ecommerce-solutions',
                'short_description' => 'Complete online store development and management',
                'description' => 'Full-featured e-commerce platforms with payment processing, inventory management, and customer relationship tools.',
                'icon' => 'fas fa-shopping-cart',
                'price_from' => 8000.00,
                'features' => ['Payment Integration', 'Inventory Management', 'Order Processing', 'Customer Accounts'],
                'is_featured' => true,
                'is_active' => true,
                'sort_order' => 3,
            ],
            [
                'name' => 'Digital Marketing',
                'slug' => 'digital-marketing',
                'short_description' => 'SEO, social media, and online advertising campaigns',
                'description' => 'Comprehensive digital marketing strategies including SEO optimization, social media management, and targeted advertising campaigns.',
                'icon' => 'fas fa-bullhorn',
                'price_from' => 2500.00,
                'features' => ['SEO Optimization', 'Social Media Management', 'PPC Campaigns', 'Analytics Reporting'],
                'is_featured' => false,
                'is_active' => true,
                'sort_order' => 4,
            ],
            [
                'name' => 'UI/UX Design',
                'slug' => 'ui-ux-design',
                'short_description' => 'User interface and experience design services',
                'description' => 'Professional UI/UX design services focusing on user-centered design principles and modern design trends.',
                'icon' => 'fas fa-paint-brush',
                'price_from' => 3500.00,
                'features' => ['User Research', 'Wireframing', 'Prototyping', 'Design Systems'],
                'is_featured' => false,
                'is_active' => true,
                'sort_order' => 5,
            ],
            [
                'name' => 'Brand Identity',
                'slug' => 'brand-identity',
                'short_description' => 'Logo design and complete brand identity packages',
                'description' => 'Create memorable brand identities including logo design, color schemes, typography, and brand guidelines.',
                'icon' => 'fas fa-palette',
                'price_from' => 2000.00,
                'features' => ['Logo Design', 'Brand Guidelines', 'Color Schemes', 'Typography'],
                'is_featured' => false,
                'is_active' => true,
                'sort_order' => 6,
            ],
        ];

        foreach ($services as $serviceData) {
            Service::firstOrCreate(
                ['slug' => $serviceData['slug']],
                array_merge($serviceData, [
                    'uuid' => Str::uuid(),
                    'meta_title' => $serviceData['name'] . ' - Professional ' . $serviceData['name'] . ' Services',
                    'meta_description' => $serviceData['short_description'],
                    'meta_keywords' => strtolower(str_replace(' ', ', ', $serviceData['name'])) . ', professional services, digital agency',
                ])
            );
        }
    }

    /**
     * Create client users for the projects.
     */
    private function createClientUsers(): void
    {
        $clientRole = Role::where('name', 'client')->first();
        
        if (!$clientRole) {
            return; // Skip if client role doesn't exist
        }

        $clients = [
            [
                'first_name' => 'Sarah',
                'last_name' => 'Johnson',
                'email' => '<EMAIL>',
                'company' => 'TechStartup Inc.',
            ],
            [
                'first_name' => 'Michael',
                'last_name' => 'Chen',
                'email' => '<EMAIL>',
                'company' => 'RetailPlus Solutions',
            ],
            [
                'first_name' => 'Emma',
                'last_name' => 'Williams',
                'email' => '<EMAIL>',
                'company' => 'HealthCare App Co.',
            ],
            [
                'first_name' => 'David',
                'last_name' => 'Rodriguez',
                'email' => '<EMAIL>',
                'company' => 'Food Delivery Express',
            ],
            [
                'first_name' => 'Lisa',
                'last_name' => 'Thompson',
                'email' => '<EMAIL>',
                'company' => 'Fashion Brand Studio',
            ],
        ];

        foreach ($clients as $clientData) {
            User::firstOrCreate(
                ['email' => $clientData['email']],
                array_merge($clientData, [
                    'uuid' => Str::uuid(),
                    'password' => bcrypt('password'),
                    'phone' => '+27' . rand(*********, *********),
                    'role_id' => $clientRole->id,
                    'is_active' => true,
                    'is_deleted' => false,
                    'email_verified_at' => now(),
                ])
            );
        }
    }

    /**
     * Create projects for each service.
     */
    private function createProjects(): void
    {
        $services = Service::active()->get();
        $clients = User::whereHas('role', function($query) {
            $query->where('name', 'client');
        })->get();

        if ($clients->isEmpty()) {
            // If no clients exist, use any user or create a default one
            $clients = User::take(5)->get();
        }

        $projectTemplates = [
            'Web Development' => [
                [
                    'title' => 'Corporate Website Redesign',
                    'description' => 'Complete redesign of corporate website with modern responsive design and improved user experience.',
                    'content' => 'This project involved a complete overhaul of the client\'s existing website, implementing modern design principles, responsive layouts, and improved navigation. The new site features enhanced performance, better SEO optimization, and a content management system for easy updates.',
                    'status' => 'completed',
                    'project_url' => 'https://example-corporate.com',
                    'estimated_hours' => 120,
                    'actual_hours' => 135,
                    'hourly_rate' => 75.00,
                    'total_amount' => 10125.00,
                    'is_published' => true,
                    'is_featured' => true,
                ],
                [
                    'title' => 'E-learning Platform Development',
                    'description' => 'Custom e-learning platform with course management, student tracking, and interactive content delivery.',
                    'content' => 'Developed a comprehensive e-learning platform featuring course creation tools, student progress tracking, interactive quizzes, and video content delivery. The platform includes user authentication, payment processing, and detailed analytics.',
                    'status' => 'completed',
                    'project_url' => 'https://example-elearning.com',
                    'estimated_hours' => 200,
                    'actual_hours' => 185,
                    'hourly_rate' => 80.00,
                    'total_amount' => 14800.00,
                    'is_published' => true,
                    'is_featured' => false,
                ],
                [
                    'title' => 'Real Estate Portal',
                    'description' => 'Property listing website with advanced search, virtual tours, and agent management system.',
                    'content' => 'Built a comprehensive real estate portal featuring property listings, advanced search filters, virtual tour integration, and agent management tools. The platform includes map integration, mortgage calculators, and lead management.',
                    'status' => 'in_progress',
                    'estimated_hours' => 180,
                    'actual_hours' => 95,
                    'hourly_rate' => 85.00,
                    'total_amount' => 15300.00,
                    'is_published' => true,
                    'is_featured' => false,
                ],
            ],
            'Mobile App Development' => [
                [
                    'title' => 'Fitness Tracking Mobile App',
                    'description' => 'Cross-platform mobile app for fitness tracking with workout plans and progress monitoring.',
                    'content' => 'Developed a comprehensive fitness tracking application with workout planning, progress monitoring, nutrition tracking, and social features. The app includes integration with wearable devices and cloud synchronization.',
                    'status' => 'completed',
                    'project_url' => 'https://apps.apple.com/fitness-tracker',
                    'estimated_hours' => 300,
                    'actual_hours' => 320,
                    'hourly_rate' => 90.00,
                    'total_amount' => 28800.00,
                    'is_published' => true,
                    'is_featured' => true,
                ],
                [
                    'title' => 'Food Delivery App',
                    'description' => 'On-demand food delivery application with real-time tracking and payment integration.',
                    'content' => 'Created a full-featured food delivery app with restaurant listings, menu management, real-time order tracking, and integrated payment processing. Includes separate apps for customers, restaurants, and delivery drivers.',
                    'status' => 'completed',
                    'project_url' => 'https://play.google.com/food-delivery',
                    'estimated_hours' => 400,
                    'actual_hours' => 425,
                    'hourly_rate' => 95.00,
                    'total_amount' => 40375.00,
                    'is_published' => true,
                    'is_featured' => true,
                ],
                [
                    'title' => 'Healthcare Appointment App',
                    'description' => 'Medical appointment booking app with doctor profiles and telemedicine features.',
                    'content' => 'Developed a healthcare app enabling patients to book appointments, view doctor profiles, access medical records, and conduct video consultations. Features include prescription management and health tracking.',
                    'status' => 'review',
                    'estimated_hours' => 250,
                    'actual_hours' => 210,
                    'hourly_rate' => 100.00,
                    'total_amount' => 25000.00,
                    'is_published' => false,
                    'is_featured' => false,
                ],
            ],
            'E-commerce Solutions' => [
                [
                    'title' => 'Fashion E-commerce Platform',
                    'description' => 'Complete online fashion store with inventory management and payment processing.',
                    'content' => 'Built a comprehensive fashion e-commerce platform featuring product catalogs, size guides, wishlist functionality, and secure payment processing. Includes admin panel for inventory and order management.',
                    'status' => 'completed',
                    'project_url' => 'https://example-fashion.com',
                    'estimated_hours' => 160,
                    'actual_hours' => 175,
                    'hourly_rate' => 80.00,
                    'total_amount' => 14000.00,
                    'is_published' => true,
                    'is_featured' => true,
                ],
                [
                    'title' => 'Electronics Marketplace',
                    'description' => 'Multi-vendor electronics marketplace with advanced search and comparison features.',
                    'content' => 'Developed a multi-vendor marketplace for electronics with product comparison tools, vendor management, commission tracking, and integrated shipping solutions.',
                    'status' => 'completed',
                    'project_url' => 'https://example-electronics.com',
                    'estimated_hours' => 220,
                    'actual_hours' => 240,
                    'hourly_rate' => 85.00,
                    'total_amount' => 20400.00,
                    'is_published' => true,
                    'is_featured' => false,
                ],
            ],
            'Digital Marketing' => [
                [
                    'title' => 'SEO Campaign for Law Firm',
                    'description' => 'Comprehensive SEO strategy and implementation for legal services website.',
                    'content' => 'Executed a complete SEO overhaul including keyword research, content optimization, technical SEO improvements, and local search optimization resulting in 300% increase in organic traffic.',
                    'status' => 'completed',
                    'estimated_hours' => 80,
                    'actual_hours' => 85,
                    'hourly_rate' => 65.00,
                    'total_amount' => 5525.00,
                    'is_published' => true,
                    'is_featured' => false,
                ],
                [
                    'title' => 'Social Media Marketing Campaign',
                    'description' => 'Multi-platform social media strategy and content creation for restaurant chain.',
                    'content' => 'Developed and executed a comprehensive social media strategy across Facebook, Instagram, and TikTok, including content creation, community management, and paid advertising campaigns.',
                    'status' => 'in_progress',
                    'estimated_hours' => 120,
                    'actual_hours' => 60,
                    'hourly_rate' => 55.00,
                    'total_amount' => 6600.00,
                    'is_published' => true,
                    'is_featured' => false,
                ],
            ],
            'UI/UX Design' => [
                [
                    'title' => 'Banking App UI/UX Redesign',
                    'description' => 'Complete user interface and experience redesign for mobile banking application.',
                    'content' => 'Redesigned the entire user experience for a mobile banking app, focusing on accessibility, security, and ease of use. Included user research, wireframing, prototyping, and usability testing.',
                    'status' => 'completed',
                    'estimated_hours' => 100,
                    'actual_hours' => 110,
                    'hourly_rate' => 70.00,
                    'total_amount' => 7700.00,
                    'is_published' => true,
                    'is_featured' => true,
                ],
                [
                    'title' => 'SaaS Dashboard Design',
                    'description' => 'User interface design for enterprise software dashboard with complex data visualization.',
                    'content' => 'Created an intuitive dashboard design for enterprise software featuring complex data visualizations, customizable widgets, and responsive layouts for various screen sizes.',
                    'status' => 'review',
                    'estimated_hours' => 90,
                    'actual_hours' => 75,
                    'hourly_rate' => 75.00,
                    'total_amount' => 6750.00,
                    'is_published' => false,
                    'is_featured' => false,
                ],
            ],
            'Brand Identity' => [
                [
                    'title' => 'Tech Startup Brand Identity',
                    'description' => 'Complete brand identity package including logo, color scheme, and brand guidelines.',
                    'content' => 'Developed a comprehensive brand identity for a tech startup including logo design, color palette, typography selection, and detailed brand guidelines for consistent application across all media.',
                    'status' => 'completed',
                    'estimated_hours' => 60,
                    'actual_hours' => 65,
                    'hourly_rate' => 60.00,
                    'total_amount' => 3900.00,
                    'is_published' => true,
                    'is_featured' => false,
                ],
                [
                    'title' => 'Restaurant Chain Rebranding',
                    'description' => 'Brand refresh for established restaurant chain including logo update and marketing materials.',
                    'content' => 'Executed a complete brand refresh for a restaurant chain, updating the logo, creating new marketing materials, and developing brand guidelines for franchise locations.',
                    'status' => 'completed',
                    'project_url' => 'https://example-restaurant.com',
                    'estimated_hours' => 80,
                    'actual_hours' => 85,
                    'hourly_rate' => 65.00,
                    'total_amount' => 5525.00,
                    'is_published' => true,
                    'is_featured' => false,
                ],
            ],
        ];

        // Create projects from templates
        $this->createProjectsFromTemplates($projectTemplates, $services, $clients);
    }

    /**
     * Create projects from templates.
     */
    private function createProjectsFromTemplates(array $templates, $services, $clients): void
    {
        foreach ($services as $service) {
            $serviceProjects = $templates[$service->name] ?? [];
            
            // If no specific templates, create generic projects
            if (empty($serviceProjects)) {
                $serviceProjects = $this->generateGenericProjects($service->name);
            }

            foreach ($serviceProjects as $index => $projectData) {
                $client = $clients->random();
                
                $project = Project::firstOrCreate(
                    ['title' => $projectData['title']],
                    array_merge($projectData, [
                        'uuid' => Str::uuid(),
                        'slug' => Str::slug($projectData['title']) . '-' . rand(1000, 9999),
                        'client_id' => $client->id,
                        'client_name' => $client->company ?? $client->first_name . ' ' . $client->last_name,
                        'service_id' => $service->id,
                        'start_date' => now()->subMonths(rand(1, 12)),
                        'end_date' => $projectData['status'] === 'completed' ? now()->subDays(rand(1, 30)) : null,
                        'currency_code' => 'ZAR',
                        'priority' => ['low', 'medium', 'high'][array_rand(['low', 'medium', 'high'])],
                        'meta_title' => $projectData['title'] . ' - ' . $service->name . ' Project',
                        'meta_description' => $projectData['description'],
                        'meta_keywords' => strtolower(str_replace(' ', ', ', $projectData['title'])) . ', ' . strtolower($service->name),
                        'is_deleted' => false,
                    ])
                );
            }
        }
    }

    /**
     * Generate generic projects for services without specific templates.
     */
    private function generateGenericProjects(string $serviceName): array
    {
        return [
            [
                'title' => $serviceName . ' Project Alpha',
                'description' => 'Professional ' . strtolower($serviceName) . ' solution with modern features and best practices.',
                'content' => 'This project showcases our expertise in ' . strtolower($serviceName) . ' with a focus on quality, performance, and user experience.',
                'status' => 'completed',
                'estimated_hours' => rand(50, 200),
                'actual_hours' => rand(45, 220),
                'hourly_rate' => rand(60, 120),
                'total_amount' => rand(5000, 25000),
                'is_published' => true,
                'is_featured' => false,
            ],
            [
                'title' => $serviceName . ' Project Beta',
                'description' => 'Innovative ' . strtolower($serviceName) . ' implementation with cutting-edge technology.',
                'content' => 'An advanced project demonstrating our capabilities in ' . strtolower($serviceName) . ' using the latest technologies and methodologies.',
                'status' => 'in_progress',
                'estimated_hours' => rand(80, 300),
                'actual_hours' => rand(40, 150),
                'hourly_rate' => rand(70, 130),
                'total_amount' => rand(8000, 35000),
                'is_published' => true,
                'is_featured' => false,
            ],
        ];
    }
}
