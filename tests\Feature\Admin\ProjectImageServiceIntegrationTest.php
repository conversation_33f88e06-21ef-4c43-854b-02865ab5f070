<?php

namespace Tests\Feature\Admin;

use App\Models\Project;
use App\Models\User;
use App\Models\Role;
use App\Services\ActivityLogger;
use App\Services\ImageService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Storage;
use Tests\TestCase;

class ProjectImageServiceIntegrationTest extends TestCase
{
    use RefreshDatabase;

    protected User $admin;
    protected User $client;

    protected function setUp(): void
    {
        parent::setUp();

        // Create roles
        $adminRole = Role::factory()->create(['name' => 'admin']);
        $clientRole = Role::factory()->create(['name' => 'client']);

        // Create users
        $this->admin = User::factory()->create(['role_id' => $adminRole->id]);
        $this->client = User::factory()->create(['role_id' => $clientRole->id]);

        // Mock services
        $this->mock(ActivityLogger::class);
    }

    /** @test */
    public function project_model_returns_correct_image_url_using_image_service()
    {
        // Mock ImageService
        $imageService = $this->mock(ImageService::class);
        $imageService->shouldReceive('getImageUrl')
            ->with('projects/test-image.jpg')
            ->andReturn('http://localhost:8000/storage/projects/test-image.jpg');

        $project = Project::factory()->create([
            'featured_image' => 'projects/test-image.jpg'
        ]);

        $imageUrl = $project->featured_image_url;

        $this->assertEquals('http://localhost:8000/storage/projects/test-image.jpg', $imageUrl);
    }

    /** @test */
    public function project_model_returns_placeholder_when_no_image()
    {
        $project = Project::factory()->create([
            'featured_image' => null
        ]);

        $imageUrl = $project->featured_image_url;

        $this->assertStringContainsString('images/projects/placeholder.jpg', $imageUrl);
    }

    /** @test */
    public function project_create_uses_image_service_correctly()
    {
        Storage::fake('public');
        
        // Mock ImageService with correct response structure
        $imageService = $this->mock(ImageService::class);
        $imageService->shouldReceive('processUploadedImage')
            ->once()
            ->with(\Mockery::type(UploadedFile::class), [
                'subdirectory' => 'projects',
                'create_variants' => true,
                'create_webp' => true,
            ])
            ->andReturn([
                'success' => true,
                'original_path' => 'projects/new-project-image.jpg',
                'webp_path' => 'projects/new-project-image.webp',
                'variants' => [],
                'file_info' => ['size' => 1024, 'mime' => 'image/jpeg'],
                'processing_time_ms' => 150.5
            ]);

        $file = UploadedFile::fake()->image('project.jpg', 1200, 800);

        $projectData = [
            'title' => 'New Project with Image',
            'description' => 'Test project description',
            'client_id' => $this->client->id,
            'client_name' => $this->client->first_name . ' ' . $this->client->last_name,
            'status' => 'planning',
            'featured_image' => $file,
        ];

        $response = $this->actingAs($this->admin)
            ->post(route('admin.projects.store'), $projectData);

        $response->assertRedirect(route('admin.projects.index'));
        
        $this->assertDatabaseHas('projects', [
            'title' => 'New Project with Image',
            'featured_image' => 'projects/new-project-image.jpg',
        ]);
    }

    /** @test */
    public function project_update_uses_image_service_correctly()
    {
        Storage::fake('public');
        
        $project = Project::factory()->create([
            'client_id' => $this->client->id,
            'featured_image' => 'projects/old-image.jpg'
        ]);

        // Mock ImageService
        $imageService = $this->mock(ImageService::class);
        $imageService->shouldReceive('deleteImageVariants')
            ->once()
            ->with('projects/old-image.jpg')
            ->andReturn(true);
        $imageService->shouldReceive('processUploadedImage')
            ->once()
            ->andReturn([
                'success' => true,
                'original_path' => 'projects/updated-image.jpg',
                'webp_path' => 'projects/updated-image.webp',
                'variants' => [],
                'file_info' => ['size' => 1024, 'mime' => 'image/jpeg'],
                'processing_time_ms' => 150.5
            ]);

        $file = UploadedFile::fake()->image('updated.jpg', 1200, 800);

        $updateData = [
            'title' => $project->title,
            'description' => $project->description,
            'client_id' => $this->client->id,
            'client_name' => $this->client->first_name . ' ' . $this->client->last_name,
            'status' => 'completed',
            'featured_image' => $file,
        ];

        $response = $this->actingAs($this->admin)
            ->put(route('admin.projects.update', $project), $updateData);

        $response->assertRedirect(route('admin.projects.show', $project));
        
        $this->assertDatabaseHas('projects', [
            'id' => $project->id,
            'featured_image' => 'projects/updated-image.jpg',
        ]);
    }

    /** @test */
    public function project_create_handles_image_service_failure()
    {
        Storage::fake('public');
        
        // Mock ImageService with failure response
        $imageService = $this->mock(ImageService::class);
        $imageService->shouldReceive('processUploadedImage')
            ->once()
            ->andReturn([
                'success' => false,
                'errors' => ['File too large', 'Invalid format']
            ]);

        $file = UploadedFile::fake()->image('project.jpg', 1200, 800);

        $projectData = [
            'title' => 'Failed Project',
            'description' => 'Test project description',
            'client_id' => $this->client->id,
            'client_name' => $this->client->first_name . ' ' . $this->client->last_name,
            'status' => 'planning',
            'featured_image' => $file,
        ];

        $response = $this->actingAs($this->admin)
            ->post(route('admin.projects.store'), $projectData);

        $response->assertRedirect();
        $response->assertSessionHasErrors(['featured_image']);
        
        // Ensure the project wasn't created due to image failure
        $this->assertDatabaseMissing('projects', [
            'title' => 'Failed Project',
        ]);
    }

    /** @test */
    public function project_update_handles_image_service_failure()
    {
        Storage::fake('public');
        
        $project = Project::factory()->create([
            'client_id' => $this->client->id,
            'title' => 'Original Title'
        ]);

        // Mock ImageService with failure response
        $imageService = $this->mock(ImageService::class);
        $imageService->shouldReceive('processUploadedImage')
            ->once()
            ->andReturn([
                'success' => false,
                'errors' => ['Virus detected', 'File corrupted']
            ]);

        $file = UploadedFile::fake()->image('bad-image.jpg', 1200, 800);

        $updateData = [
            'title' => 'Updated Title',
            'description' => $project->description,
            'client_id' => $this->client->id,
            'client_name' => $this->client->first_name . ' ' . $this->client->last_name,
            'status' => 'completed',
            'featured_image' => $file,
        ];

        $response = $this->actingAs($this->admin)
            ->put(route('admin.projects.update', $project), $updateData);

        $response->assertRedirect();
        $response->assertSessionHasErrors(['featured_image']);
        
        // Ensure the project wasn't updated due to image failure
        $this->assertDatabaseHas('projects', [
            'id' => $project->id,
            'title' => 'Original Title', // Should remain unchanged
        ]);
    }

    /** @test */
    public function project_gallery_images_use_image_service_for_urls()
    {
        // Mock ImageService
        $imageService = $this->mock(ImageService::class);
        $imageService->shouldReceive('getImageUrl')
            ->with('projects/gallery1.jpg')
            ->andReturn('http://localhost:8000/storage/projects/gallery1.jpg');
        $imageService->shouldReceive('getImageUrl')
            ->with('projects/gallery2.jpg')
            ->andReturn('http://localhost:8000/storage/projects/gallery2.jpg');
        $imageService->shouldReceive('getImageUrl')
            ->with('projects/featured.jpg')
            ->andReturn('http://localhost:8000/storage/projects/featured.jpg');

        $project = Project::factory()->create([
            'featured_image' => 'projects/featured.jpg',
            'gallery' => ['projects/gallery1.jpg', 'projects/gallery2.jpg']
        ]);

        $allImages = $project->all_images;

        $this->assertCount(3, $allImages);
        $this->assertContains('http://localhost:8000/storage/projects/featured.jpg', $allImages);
        $this->assertContains('http://localhost:8000/storage/projects/gallery1.jpg', $allImages);
        $this->assertContains('http://localhost:8000/storage/projects/gallery2.jpg', $allImages);
    }

    /** @test */
    public function admin_project_views_display_correct_image_urls()
    {
        // Mock ImageService
        $imageService = $this->mock(ImageService::class);
        $imageService->shouldReceive('getImageUrl')
            ->with('projects/test-image.jpg')
            ->andReturn('http://localhost:8000/storage/projects/test-image.jpg');

        $project = Project::factory()->create([
            'featured_image' => 'projects/test-image.jpg'
        ]);

        // Test admin index view
        $response = $this->actingAs($this->admin)
            ->get(route('admin.projects.index'));

        $response->assertStatus(200);
        $response->assertSee('http://localhost:8000/storage/projects/test-image.jpg');

        // Test admin show view
        $response = $this->actingAs($this->admin)
            ->get(route('admin.projects.show', $project));

        $response->assertStatus(200);
        $response->assertSee('http://localhost:8000/storage/projects/test-image.jpg');
    }

    /** @test */
    public function project_without_image_shows_placeholder()
    {
        $project = Project::factory()->create([
            'featured_image' => null
        ]);

        $response = $this->actingAs($this->admin)
            ->get(route('admin.projects.index'));

        $response->assertStatus(200);
        // Should show placeholder div instead of image
        $response->assertSee('bg-gray-200');
    }
}
